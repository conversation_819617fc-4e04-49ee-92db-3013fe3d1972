<script lang="ts">
  import { listen } from "@tauri-apps/api/event";
  import { onMount } from "svelte";
  import { Button } from "$lib/components/ui/button";
  import { Progress } from "$lib/components/ui/progress";
  import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
  } from "$lib/components/ui/card";
  import { ActionEvents } from "$lib/constants";

  interface $$Props {
    isOpen: boolean;
    title?: string;
    currentActionIndex?: number;
    totalActions?: number;
    isAllCompleted?: boolean;
    isCancelled?: boolean;
    onClose?: () => void;
    onCancel?: () => void;
  }

  let {
    isOpen = false,
    title = "处理中...",
    currentActionIndex = 1,
    totalActions = 1,
    isAllCompleted = false,
    isCancelled = false,
    onClose,
    onCancel,
  }: $$Props = $props();

  let currentProgress = $state(0);
  let overallProgress = $state(0);
  let isProcessing = $state(false);

  // 当动作索引变化时，重置当前进度
  $effect(() => {
    // 通过访问 currentActionIndex 来追踪其变化
    currentActionIndex;
    currentProgress = 0;
  });

  // 计算总体进度
  $effect(() => {
    if (totalActions > 0) {
      overallProgress =
        ((currentActionIndex - 1) * 100 + currentProgress) / totalActions;
    }
  });

  // 统一的进度事件处理函数
  function handleProgressEvent(event: any) {
    const payload = event.payload as { pct: number };
    currentProgress = payload.pct;
    isProcessing = true;
  }

  onMount(() => {
    // 需要监听的所有进度事件
    const progressEvents = [
      ActionEvents.TRIM_PROGRESS,
      ActionEvents.COLOR_PROGRESS,
      ActionEvents.AUDIO_PROGRESS,
      ActionEvents.CROP_PROGRESS,
      ActionEvents.TRANSFORM_PROGRESS,
      ActionEvents.EFFECT_PROGRESS,
      ActionEvents.WATERMARK_PROGRESS,
      ActionEvents.ENCODE_PROGRESS,
      ActionEvents.VIDEOTOIMAGE_PROGRESS,
      ActionEvents.VIDEOTOGIF_PROGRESS,
      ActionEvents.BATCH_IMAGE_PROGRESS,
      ActionEvents.IMAGE_TO_VIDEO_PROGRESS,
    ];

    // 批量注册事件监听器
    const unlistenFunctions = progressEvents.map((eventName) =>
      listen(eventName, handleProgressEvent)
    );

    // 返回清理函数
    return () => {
      Promise.all(unlistenFunctions).then((unlisteners) => {
        unlisteners.forEach((unlisten) => unlisten());
      });
    };
  });

  function handleClose() {
    if (!isProcessing || isAllCompleted || isCancelled) {
      if (onClose) onClose();
    }
  }

  function handleCancel() {
    if (onCancel) onCancel();
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <Card class="w-96">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          {#if isProcessing && !isAllCompleted && !isCancelled}
            <div
              class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"
            ></div>
          {/if}
          {title}
          {#if totalActions > 1}
            <span class="text-sm text-muted-foreground font-normal">
              ({currentActionIndex}/{totalActions})
            </span>
          {/if}
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        {#if isCancelled}
          <div class="text-center text-orange-600 font-medium">
            ⚠️ 处理已取消
          </div>
        {:else if isAllCompleted}
          <div class="text-center text-green-600 font-medium">
            ✅ 所有动作处理完成！
          </div>
        {:else}
          <!-- 当前动作进度 -->
          <div class="space-y-2">
            <div class="flex justify-between text-sm text-muted-foreground">
              <span>当前动作进度</span>
              <span>{currentProgress.toFixed(1)}%</span>
            </div>
            <Progress value={currentProgress} max={100} />
          </div>

          <!-- 总体进度 -->
          {#if totalActions > 1}
            <div class="space-y-2">
              <div class="flex justify-between text-sm text-muted-foreground">
                <span>总体进度</span>
                <span>{overallProgress.toFixed(1)}%</span>
              </div>
              <Progress value={overallProgress} max={100} />
            </div>
          {/if}

          {#if currentProgress >= 100}
            <div class="text-center text-blue-600 font-medium">
              ⏳ 当前动作完成，准备处理下一个...
            </div>
          {/if}
        {/if}

        <div class="flex justify-end gap-2">
          {#if !isAllCompleted && !isCancelled && isProcessing}
            <Button variant="destructive" onclick={handleCancel}>取消</Button>
          {/if}
          {#if isAllCompleted || isCancelled}
            <Button variant="outline" onclick={handleClose}>关闭</Button>
          {/if}
        </div>
      </CardContent>
    </Card>
  </div>
{/if}
